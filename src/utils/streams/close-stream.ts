import type { ClientReadableStream } from '@grpc/grpc-js'
import { createDeferredWithTimeout } from '@kdt310722/utils/promise'
import { StreamError } from '../../errors'
import { isCancelledGrpcStatus, isGrpcError } from '../grpc'

export function isStreamClosed<T>(stream: ClientReadableStream<T>) {
    return stream.closed || stream.destroyed || stream.readableEnded
}

export interface CloseStreamOptions {
    timeout?: number
    destroyOnFail?: boolean
    onError?: (error: unknown) => void
}

export async function closeStream<T>(stream: ClientReadableStream<T>, { timeout = 10_000, destroyOnFail = true, onError }: CloseStreamOptions = {}) {
    if (isStreamClosed(stream)) {
        return
    }

    const promise = createDeferredWithTimeout<void>(timeout, () => new StreamError('Stream close timeout'))
    const closeHandler = () => promise.resolve()
    const errorHandler = (error: unknown) => (isGrpcError(error) && isCancelledGrpcStatus(error) ? promise.resolve() : promise.reject(error))

    stream.once('end', closeHandler)
    stream.once('status', closeHandler)
    stream.once('error', errorHandler)
    stream.once('close', closeHandler)

    try {
        stream.cancel()
    } catch (error) {
        if (promise.isSettled) {
            onError?.(error)
        } else {
            promise.reject(error)
        }
    }

    const cleanup = () => {
        stream.removeListener('end', closeHandler)
        stream.removeListener('status', closeHandler)
        stream.removeListener('error', errorHandler)
        stream.removeListener('close', closeHandler)
    }

    const handleError = (error: unknown) => {
        if (destroyOnFail) {
            onError?.(error)
            stream.destroy()
        } else {
            throw error
        }
    }

    return promise.catch(handleError).finally(cleanup)
}
