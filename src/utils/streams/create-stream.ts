import type { ClientReadableStream } from '@grpc/grpc-js'
import { createAbortError } from '@kdt310722/utils/error'
import type { Fn } from '@kdt310722/utils/function'
import { type Awaitable, createDeferredWithTimeout } from '@kdt310722/utils/promise'
import { StreamError } from '../../errors'

export interface CreateStreamOptions {
    timeout?: number
    signal?: AbortSignal
}

export async function subscribeStream<TData, TStream extends ClientReadableStream<TData> = ClientReadableStream<TData>>(createStream: (signal: AbortSignal) => Awaitable<TStream>, { timeout = 30_000, signal }: CreateStreamOptions = {}): Promise<TStream> {
    if (signal?.aborted) {
        throw new StreamError('Subscription aborted before it started')
    }

    const abortController = new AbortController()
    const abortSignal = AbortSignal.any([abortController.signal, ...(signal ? [signal] : [])])

    const promise = createDeferredWithTimeout<TStream>(timeout, () => new StreamError('Subscribe timeout'), {
        onSettle: () => abortSignal.aborted || abortController.abort(),
    })

    const handleAbort = () => {
        promise.reject(abortSignal.reason ?? createAbortError('Subscription aborted'))
    }

    abortSignal.addEventListener('abort', handleAbort, {
        once: true,
    })

    let cleanup: Fn | undefined

    try {
        const stream = await createStream(abortSignal)

        const handlers = {
            metadata: () => promise.resolve(stream),
            end: () => promise.reject(new StreamError('Stream ended before subscription completed')),
            error: (error: unknown) => promise.reject(error),
            status: (status) => promise.reject(new StreamError('Stream status received before subscription completed').withValue('status', status)),
            close: () => promise.reject(new StreamError('Stream closed before subscription completed')),
        }

        cleanup = () => {
            for (const [event, handler] of Object.entries(handlers)) {
                stream.removeListener(event, handler)
            }
        }

        for (const [event, handler] of Object.entries(handlers)) {
            stream.once(event, handler)
        }
    } catch (error) {
        promise.reject(error)
    }

    return promise.finally(() => {
        abortSignal.removeEventListener('abort', handleAbort)
        cleanup?.()
    })
}
